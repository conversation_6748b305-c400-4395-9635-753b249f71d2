ig.module('game.entities.objects.popup-tutorial')
.requires(
    'plugins.utils.objects.popup-base',
    'game.entities.text',
    'game.entities.buttons.button-nav'
)
.defines(function () {

    EntityPopupTutorial = EntityPopupBase.extend({
        name: 'popup-tutorial',

        // --- Configuration for the Tutorial Popup ---

        headerTextConfig: {
            text: _STRINGS.Game.Tutorial.Header,
            fontSize: 72,
            fontFamily: 'bebasneue-bold',
            fontColor: '#FFFFFF',
            align: 'center',
            vAlign: 'middle',
            height: 80,
            shadowEnabled: true,
            shadowOffsetX: 4,
            shadowOffsetY: 4,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },

        bodyTextConfig: {
            text: '',
            fontSize: 36,
            fontFamily: 'bebasneue-bold',
            fontColor: '#ffffff',
            align: 'center',
            vAlign: 'middle',
            shadowEnabled: true,
            shadowOffsetX: 2,
            shadowOffsetY: 2,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },

        pageIndicatorTextConfig: {
            text: '1/2',
            fontSize: 28,
            fontFamily: 'bebasneue-bold',
            fontColor: '#AAAAAA',
            align: 'center',
            vAlign: 'middle',
            shadowEnabled: true,
            shadowOffsetX: 1,
            shadowOffsetY: 1,
            shadowBlur: 1,
            shadowColor: '#000000',
            overflow: true
        },

        headerTextOffset: { x: 0, y: 40 },
        bodyTextOffset: { x: 0, y: 420 },
        pageIndicatorTextOffset: { x: 0, y: -60 },

        tutorialImageOffset: { x: 0, y: 120 },
        tutorialImageAlpha: 1,
        tutorialImageWidth: 670,
        tutorialImageHeight: 340,

        displayOverlay: true,
        opacityCap: 0.4,
        hasCloseButton: true,

        tutorialPages: [
            {
                bodyText: _STRINGS.Game.Tutorial.Body[0],
                image: new ig.Image('media/graphics/sprites/tutorial/tutorial-1.png')
            },
            {
                bodyText: _STRINGS.Game.Tutorial.Body[1],
                image: new ig.Image('media/graphics/sprites/tutorial/tutorial-2.png')
            }
        ],

        currentPageIndex: 0,
        isTransitioning: false,
        transitionDuration: 200,

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            this.elements.buttons = {};

            this.elements.buttons.next = this.spawnEntity(
                EntityButtonNext,
                this.pos.x,
                this.pos.y,
                { _parent: this }
            );
            this.elements.buttons.prev = this.spawnEntity(
                EntityButtonPrev,
                this.pos.x,
                this.pos.y,
                { _parent: this }
            );

            this.createBodyText();
            this.createPageIndicatorText();
            this.updatePageContent();
            this.updateButtonStates();

            ig.game.sortEntitiesDeferred();
        },

        createBodyText: function () {
            var currentBodyTextConfig = {};
            currentBodyTextConfig.width = currentBodyTextConfig.width || this.size.x;
            currentBodyTextConfig.height = currentBodyTextConfig.height || (this.size.y * 0.25);
            ig.merge(currentBodyTextConfig, this.bodyTextConfig);

            var currentBodyTextOffset = {};
            ig.merge(currentBodyTextOffset, this.bodyTextOffset);

            var bodyTextEntitySettings = {
                textConfig: currentBodyTextConfig,
                zIndex: this.zIndex + 1,
                alpha: 0 // Start transparent, will be updated by popup
            };

            var initialBodyX = this.pos.x + (currentBodyTextOffset.x || 0);
            var initialBodyY = this.pos.y + this.tutorialImageOffset.y + this.tutorialImageHeight + currentBodyTextOffset.x;

            this.elements.bodyText = ig.game.spawnEntity(
                EntityText,
                initialBodyX,
                initialBodyY,
                bodyTextEntitySettings
            );
        },

        createPageIndicatorText: function () {
            var currentPageIndicatorTextConfig = {};
            currentPageIndicatorTextConfig.width = currentPageIndicatorTextConfig.width || this.size.x;
            currentPageIndicatorTextConfig.height = currentPageIndicatorTextConfig.height || (this.size.y * 0.1);
            ig.merge(currentPageIndicatorTextConfig, this.pageIndicatorTextConfig);

            var currentPageIndicatorTextOffset = {};
            ig.merge(currentPageIndicatorTextOffset, this.pageIndicatorTextOffset);

            var pageIndicatorTextEntitySettings = {
                textConfig: currentPageIndicatorTextConfig,
                zIndex: this.zIndex + 1,
                alpha: 0 // Start transparent, will be updated by popup
            };

            var initialPageIndicatorX = this.pos.x + (currentPageIndicatorTextOffset.x || 0);
            var initialPageIndicatorY = this.pos.y + (currentPageIndicatorTextOffset.y || 0);

            this.elements.pageIndicatorText = ig.game.spawnEntity(
                EntityText,
                initialPageIndicatorX,
                initialPageIndicatorY,
                pageIndicatorTextEntitySettings
            );
        },

        updateElementsPosition: function () {
            this.parent();

            if (this.elements.headerText) this.elements.headerText._updateAnchorPosition();
            if (this.elements.bodyText) {
                this.elements.bodyText.pos.x = this.pos.x + this.bodyTextOffset.x;
                this.elements.bodyText.pos.y = this.pos.y + this.tutorialImageOffset.y + this.tutorialImageHeight + this.bodyTextOffset.x;
            }

            if (this.elements.pageIndicatorText) {
                this.elements.pageIndicatorText.pos.x = this.pos.x + (this.pageIndicatorTextOffset.x || 0);
                this.elements.pageIndicatorText.pos.y = this.pos.y + this.size.y + (this.pageIndicatorTextOffset.y || 0);
                if (this.elements.pageIndicatorText._updateAnchorPosition) {
                    this.elements.pageIndicatorText._updateAnchorPosition();
                }
            }

            if (this.elements.buttons) {
                var padding = 50;
                var tutorialY = this.pos.y + this.tutorialImageOffset.y + this.tutorialImageHeight * 0.5;
                if (this.elements.buttons.next) {
                    this.elements.buttons.next.pos.x = this.pos.x + this.size.x - this.elements.buttons.next.size.x - padding;
                    this.elements.buttons.next.pos.y = tutorialY - this.elements.buttons.next.size.y * 0.5;
                }
                if (this.elements.buttons.prev) {
                    this.elements.buttons.prev.pos.x = this.pos.x + padding;
                    this.elements.buttons.prev.pos.y = this.elements.buttons.next.pos.y;
                }
            }
        },

        updateElementsAlpha: function (newAlpha) {
            this.parent(newAlpha);

            // Update body text alpha
            if (this.elements.bodyText && this.elements.bodyText.updateAlpha) {
                this.elements.bodyText.updateAlpha(newAlpha);
            }

            // Update navigation buttons alpha
            if (this.elements.buttons) {
                if (this.elements.buttons.next && this.elements.buttons.next.updateAlpha) {
                    this.elements.buttons.next.updateAlpha(newAlpha);
                }
                if (this.elements.buttons.prev && this.elements.buttons.prev.updateAlpha) {
                    this.elements.buttons.prev.updateAlpha(newAlpha);
                }
            }
        },

        updatePageContent: function () {
            var currentPage = this.tutorialPages[this.currentPageIndex];
            if (this.elements.bodyText) {
                this.elements.bodyText.setTextContent(currentPage.bodyText);
            }

            if (this.elements.pageIndicatorText) {
                this.elements.pageIndicatorText.setTextContent((this.currentPageIndex + 1) + '/' + this.tutorialPages.length);
            }
        },

        updateButtonStates: function () {
            if (!this.elements.buttons) return;

            // Show/hide previous button
            if (this.elements.buttons.prev) {
                if (this.currentPageIndex === 0) {
                    this.elements.buttons.prev.hide();
                } else {
                    this.elements.buttons.prev.show();
                }
            }

            // Show/hide next button (hide on last page since user must close tutorial)
            if (this.elements.buttons.next) {
                if (this.currentPageIndex >= this.tutorialPages.length - 1) {
                    this.elements.buttons.next.hide();
                } else {
                    this.elements.buttons.next.show();
                }
            }
        },

        onNext: function () {
            if (this.isTransitioning || this.currentPageIndex >= this.tutorialPages.length - 1) return;
            
            this.transitionToPage(this.currentPageIndex + 1);
        },

        onPrev: function () {
            if (this.isTransitioning || this.currentPageIndex <= 0) return;
            
            this.transitionToPage(this.currentPageIndex - 1);
        },

        transitionToPage: function (newPageIndex) {
            if (newPageIndex < 0 || newPageIndex >= this.tutorialPages.length) return;

            this.isTransitioning = true;
            var self = this;

            // Fade out current content
            var fadeOutObj = { alpha: this.tutorialImageAlpha };
            new ig.TweenDef(fadeOutObj)
            .to({ alpha: 0 }, this.transitionDuration / 2)
            .onUpdate(function () {
                self.tutorialImageAlpha = fadeOutObj.alpha;
                if (self.elements.bodyText && self.elements.bodyText.updateAlpha) {
                    self.elements.bodyText.updateAlpha(fadeOutObj.alpha);
                }
            })
            .onComplete(function () {
                // Update content
                self.currentPageIndex = newPageIndex;
                self.updatePageContent();
                self.updateButtonStates();

                // Fade in new content
                var fadeInObj = { alpha: 0 };
                new ig.TweenDef(fadeInObj)
                .to({ alpha: 1 }, self.transitionDuration / 2)
                .onUpdate(function () {
                    self.tutorialImageAlpha = fadeInObj.alpha;
                    if (self.elements.bodyText && self.elements.bodyText.updateAlpha) {
                        self.elements.bodyText.updateAlpha(fadeInObj.alpha);
                    }
                })
                .onComplete(function () {
                    self.isTransitioning = false;
                })
                .start();
            })
            .start();
        },

        draw: function () {
            this.parent();

            var ctx = ig.system.context;
            var tutorialImage = this.tutorialPages[this.currentPageIndex].image;

            // Draw tutorial image (centered horizontally in popup)
            if (tutorialImage.loaded) {
                var imageX = this.pos.x + (this.size.x * 0.5) - (this.tutorialImageWidth / 2) + this.tutorialImageOffset.x - ig.game.screen.x;
                var imageY = this.pos.y + this.tutorialImageOffset.y - ig.game.screen.y;

                ctx.save();
                ctx.globalAlpha = this.popupAlpha * this.tutorialImageAlpha;
                tutorialImage.draw(
                    imageX,
                    imageY,
                    0, 0,
                    this.tutorialImageWidth,
                    this.tutorialImageHeight
                );

                ctx.restore();
            }
        },

        // Override kill to clean up all entities
        kill: function () {
            if (this.elements.buttons) {
                if (this.elements.buttons.next) this.elements.buttons.next.kill();
                if (this.elements.buttons.prev) this.elements.buttons.prev.kill();
            }

            if (this.elements.bodyText) this.elements.bodyText.kill();
            if (this.elements.pageIndicatorText) this.elements.pageIndicatorText.kill();

            this.parent();
        }
    });
});
